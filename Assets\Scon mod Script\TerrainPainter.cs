using UnityEngine;

public class TerrainPainter : MonoBehaviour
{
  public float paintRadius = 2f;
    public int targetLayerIndex = 0;     // Texture layer to check before painting (e.g. grass)
    public int applyLayerIndex = 1;      // Texture layer to apply (e.g. soil)

    private Terrain terrain;
    private TerrainData terrainData;
    private float[,,] originalAlphamaps;

    void Start()
    {
        terrain = Terrain.activeTerrain;
        if (terrain != null)
        {
            terrainData = terrain.terrainData;
            originalAlphamaps = terrainData.GetAlphamaps(0, 0,
                terrainData.alphamapWidth, terrainData.alphamapHeight);
        }
    }

    void OnCollisionStay(Collision collision)
    {
        if (terrain == null) return;

        // Check if colliding with Terrain
        if (collision.collider.GetComponent<TerrainCollider>())
        {
            // Loop through all contact points with terrain
            foreach (ContactPoint contact in collision.contacts)
            {
                if (IsTargetLayer(contact.point))
                {
                    PaintTextureAt(contact.point);
                }
            }
        }
    }

    bool IsTargetLayer(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        float[,,] alphamaps = terrainData.GetAlphamaps(mapX, mapZ, 1, 1);
        float targetWeight = alphamaps[0, 0, targetLayerIndex];

        return targetWeight >= 0.5f;
    }

    void PaintTextureAt(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        int radius = Mathf.RoundToInt(paintRadius);
        int size = radius * 2;

        int startX = Mathf.Clamp(mapX - radius, 0, terrainData.alphamapWidth - size);
        int startZ = Mathf.Clamp(mapZ - radius, 0, terrainData.alphamapHeight - size);

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, size, size);

        for (int x = 0; x < size; x++)
        {
            for (int z = 0; z < size; z++)
            {
                float currentWeight = alphaMaps[x, z, targetLayerIndex];
                if (currentWeight >= 0.5f)
                {
                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        alphaMaps[x, z, i] = (i == applyLayerIndex) ? 1f : 0f;
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.R))
        {
            terrainData.SetAlphamaps(0, 0, originalAlphamaps);
            Debug.Log("Terrain reset to original textures.");
        }
    }
}
