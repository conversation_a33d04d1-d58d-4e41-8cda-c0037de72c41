using UnityEngine;

public class TerrainPainter : MonoBehaviour
{
    public float paintRadius = 2f;
    public int targetLayerIndex = 0;     // Texture layer to check before painting (e.g. grass)
    public int applyLayerIndex = 1;      // Texture layer to apply (e.g. soil)
    public float checkInterval = 0.1f;   // How often to check for terrain contact

    private Terrain terrain;
    private TerrainData terrainData;
    private float[,,] originalAlphamaps;
    private Rigidbody rb;
    private bool isGrounded = false;

    void Start()
    {
        terrain = Terrain.activeTerrain;
        rb = GetComponent<Rigidbody>();

        if (terrain != null)
        {
            terrainData = terrain.terrainData;
            originalAlphamaps = terrainData.GetAlphamaps(0, 0,
                terrainData.alphamapWidth, terrainData.alphamapHeight);
        }

        // Start checking for terrain contact
        InvokeRepeating("CheckTerrainContact", 0f, checkInterval);
    }

    void CheckTerrainContact()
    {
        if (terrain == null || rb == null) return;

        // Raycast downward to check if we're on terrain
        RaycastHit hit;
        Vector3 rayStart = transform.position + Vector3.up * 0.5f;

        if (Physics.Raycast(rayStart, Vector3.down, out hit, 2f))
        {
            // Check if we hit the terrain
            if (hit.collider.GetComponent<TerrainCollider>())
            {
                isGrounded = true;

                // Check if we should paint at this position
                if (IsTargetLayer(hit.point))
                {
                    PaintTextureAt(hit.point);
                }
            }
            else
            {
                isGrounded = false;
            }
        }
        else
        {
            isGrounded = false;
        }
    }

    // Alternative collision method (keep both for better compatibility)
    void OnCollisionStay(Collision collision)
    {
        if (terrain == null) return;

        // Check if colliding with Terrain
        if (collision.collider.GetComponent<TerrainCollider>())
        {
            // Loop through all contact points with terrain
            foreach (ContactPoint contact in collision.contacts)
            {
                if (IsTargetLayer(contact.point))
                {
                    PaintTextureAt(contact.point);
                }
            }
        }
    }

    bool IsTargetLayer(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        float[,,] alphamaps = terrainData.GetAlphamaps(mapX, mapZ, 1, 1);
        float targetWeight = alphamaps[0, 0, targetLayerIndex];

        return targetWeight >= 0.5f;
    }

    void PaintTextureAt(Vector3 worldPos)
    {
        Vector3 terrainPos = worldPos - terrain.transform.position;

        int mapX = Mathf.RoundToInt((terrainPos.x / terrainData.size.x) * terrainData.alphamapWidth);
        int mapZ = Mathf.RoundToInt((terrainPos.z / terrainData.size.z) * terrainData.alphamapHeight);

        int radius = Mathf.RoundToInt(paintRadius);
        int size = radius * 2;

        int startX = Mathf.Clamp(mapX - radius, 0, terrainData.alphamapWidth - size);
        int startZ = Mathf.Clamp(mapZ - radius, 0, terrainData.alphamapHeight - size);

        float[,,] alphaMaps = terrainData.GetAlphamaps(startX, startZ, size, size);

        for (int x = 0; x < size; x++)
        {
            for (int z = 0; z < size; z++)
            {
                float currentWeight = alphaMaps[x, z, targetLayerIndex];
                if (currentWeight >= 0.5f)
                {
                    for (int i = 0; i < terrainData.alphamapLayers; i++)
                    {
                        alphaMaps[x, z, i] = (i == applyLayerIndex) ? 1f : 0f;
                    }
                }
            }
        }

        terrainData.SetAlphamaps(startX, startZ, alphaMaps);
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.R))
        {
            terrainData.SetAlphamaps(0, 0, originalAlphamaps);
            Debug.Log("Terrain reset to original textures.");
        }
    }
}
